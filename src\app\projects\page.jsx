import { settings } from '@/libs/siteSettings'
import React from 'react'
import BuildingsList from '@/components/BuildingsList'

export default async function ProjectsPage() {
    const responseServer=await fetch(`${settings.url}/api/buildings`)
    const data=await responseServer.json()
    // console.log('ProjectsPage:',data)
  return (
    <div className='flex w-screen h-svh'>
      <BuildingsList data={data?.buildings}/>
    </div>
  )
}
