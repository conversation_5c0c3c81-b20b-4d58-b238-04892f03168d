'use client'
import { <PERSON>vas } from '@react-three/fiber'
import React, { Suspense, useRef, useEffect, useState } from 'react'
import ExperienceAR, { ExperienceARUI } from './ExperienceAR'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import ExperienceCameraControls from './ExperienceCameraControls'
import LoadingSpinner from '../LoadingSpinner'

// New AR System Components
import ARSessionManager from './ARSessionManager'
import ARReticle, { CrosshairReticle, TargetReticle } from './ARReticle'
import ARController, { ARControllerVisual, ARTouchHandler } from './ARController'
// import ARObjectManager, { useARObjectManager } from './ARObjectGenerator'
import ExperienceModelAR from './ExperienceModelAR'
import ARDOMOverlay from './ARDOMOverlay'
import * as THREE from 'three'
export default function ExperienceWorld({data}) {
  const {experienceState}=useExperienceContext()
  const previousModeAR = useRef(experienceState?.modeAR)

  // Legacy AR state management (for old ExperienceAR component)
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)
  const [showModel, setShowModel] = useState(false)

  // Legacy refs for AR functionality
  const refModelAR = useRef(null)
  const refSessionAR = useRef(null)
  const refController = useRef(null)
  const refReticle = useRef(null)
  const glSession = useRef(null)
  const hitTestSource = useRef(null)
  const currentHitPose = useRef(null)

  // New AR System State
  const [useNewARSystem, setUseNewARSystem] = useState(true)
  const [arSessionData, setArSessionData] = useState(null)
  const [currentHitPose2, setCurrentHitPose2] = useState(null)
  const [placedModels, setPlacedModels] = useState([]) // Array of placed ExperienceModelAR instances
  const [reticleType, setReticleType] = useState('default') // 'default', 'crosshair', 'target'
  const [hitTestReady, setHitTestReady] = useState(false)
  const [sessionActive, setSessionActive] = useState(false)

  const resetModelPlacement = () => {
    console.log('Resetting model placement')
    setShowModel(false)
    if (refModelAR.current) {
      refModelAR.current.visible = false
    }
    if (refReticle.current) {
      refReticle.current.visible = false
    }
  }

  // New AR System Handlers
  const handleARSessionStart = (session) => {
    setSessionActive(true)
  }

  const handleARSessionEnd = () => {
    setCurrentHitPose2(null)
    setPlacedModels([])
    setSessionActive(false)
    setHitTestReady(false)
  }

  const handleHitTestReady = (hitTestSource) => {
    setHitTestReady(true)
  }

  const handleHitPoseUpdate = (hitPose) => {
    setCurrentHitPose2(hitPose)
  }

  const handleControllerSelect = () => {
    if (currentHitPose2 && currentHitPose2.transform && currentHitPose2.transform.matrix) {
      // Extract position from hit pose
      const matrix = new THREE.Matrix4().fromArray(currentHitPose2.transform.matrix)
      const position = new THREE.Vector3()
      const scale = new THREE.Vector3()

      matrix.decompose(position, new THREE.Quaternion(), scale)

      // Create new model placement at exact hit-test location
      const newModel = {
        id: Math.random().toString(36).substring(2, 11),
        position: [position.x, position.y, position.z],
        rotation: [0, 0, 0], // Keep building upright
        scale: [0.3, 0.3, 0.3], // Appropriate scale for AR viewing
        hitPose: currentHitPose2,
        createdAt: Date.now()
      }

      // Add to placed models (limit to 3 models max)
      setPlacedModels(prev => {
        const updated = [...prev, newModel]
        if (updated.length > 3) {
          updated.shift() // Remove oldest model
        }
        return updated
      })
    }
  }

  const clearAllModels = () => {
    setPlacedModels([])
  }

  const cycleReticleType = () => {
    const types = ['default', 'crosshair', 'target']
    const currentIndex = types.indexOf(reticleType)
    const nextIndex = (currentIndex + 1) % types.length
    setReticleType(types[nextIndex])
  }

  // Handle XR session termination when switching out of AR mode
  useEffect(() => {
    const currentModeAR = experienceState?.modeAR

    // If we were in AR mode and now we're not, terminate the XR session
    if (previousModeAR.current && !currentModeAR) {
      console.log('Switching out of AR mode, terminating XR session...')

      // Simple fallback cleanup
      const terminateActiveSession = async () => {
        try {
          const canvas = document.querySelector('canvas')
          if (canvas) {
            const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
            if (gl && gl.xr && gl.xr.getSession()) {
              console.log('Found active XR session, ending it...')
              await gl.xr.getSession().end()
              gl.xr.enabled = false
              gl.xr.setAnimationLoop(null)
              console.log('XR session terminated successfully')
            }
          }
        } catch (error) {
          console.error('Error terminating XR session:', error)
        }
      }

      terminateActiveSession()
    }

    // Update the previous mode reference
    previousModeAR.current = currentModeAR
  }, [experienceState?.modeAR])

  // console.log('ExperienceWorld:',experienceState?.enableSnapView)
  return (
    <CameraControlsErrorBoundary>
      <Canvas
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: experienceState?.modeAR && useNewARSystem ? 'none' : 'auto',
          zIndex: experienceState?.modeAR && useNewARSystem ? 1 : 1
        }}
      >
        <Suspense fallback={<LoadingSpinner/>}>
          {experienceState?.modeAR
            ? (
                useNewARSystem ? (
                  // New AR System
                  <ARSessionManager
                    onSessionStart={handleARSessionStart}
                    onSessionEnd={handleARSessionEnd}
                    onHitTestReady={handleHitTestReady}
                  >
                    {(sessionData) => (
                      <>
                        {/* Lighting for AR objects */}
                        <ambientLight intensity={0.6} />
                        <directionalLight position={[10, 10, 5]} intensity={0.8} />

                        {/* AR Reticle */}
                        {sessionData.hitTestReady && (
                          <>
                            {reticleType === 'default' && (
                              <ARReticle
                                hitTestSource={sessionData.hitTestSource}
                                onHitPoseUpdate={handleHitPoseUpdate}
                                visible={true}
                              />
                            )}
                            {reticleType === 'crosshair' && (
                              <CrosshairReticle
                                hitTestSource={sessionData.hitTestSource}
                                onHitPoseUpdate={handleHitPoseUpdate}
                                visible={true}
                              />
                            )}
                            {reticleType === 'target' && (
                              <TargetReticle
                                hitTestSource={sessionData.hitTestSource}
                                onHitPoseUpdate={handleHitPoseUpdate}
                                visible={true}
                              />
                            )}
                          </>
                        )}



                        {/* AR Controller */}
                        <ARController
                          onSelect={handleControllerSelect}
                          enabled={sessionData.isSessionActive}
                        />

                        {/* AR Controller Visual */}
                        <ARControllerVisual
                          controllerIndex={0}
                          showRay={true}
                          rayColor="#ffffff"
                          rayLength={5}
                        />

                        {/* Placed ExperienceModelAR instances */}
                        {placedModels.map((model) => {
                          const modelRef = useRef()
                          return (
                            <group
                              key={model.id}
                              position={model.position}
                              rotation={model.rotation}
                              scale={model.scale}
                            >
                              <ExperienceModelAR
                                data={data}
                                refModelAR={modelRef}
                              />
                            </group>
                          )
                        })}

                        {/* Touch Handler for mobile */}
                        <ARTouchHandler
                          onTap={handleControllerSelect}
                          enabled={sessionData.isSessionActive}
                        />
                      </>
                    )}
                  </ARSessionManager>
                ) : (
                  // Legacy AR System
                  <ExperienceAR
                    data={data}
                    refModelAR={refModelAR}
                    refSessionAR={refSessionAR}
                    refController={refController}
                    refReticle={refReticle}
                    glSession={glSession}
                    hitTestSource={hitTestSource}
                    currentHitPose={currentHitPose}
                    setErrorMessage={setErrorMessage}
                    setShowError={setShowError}
                    showModel={showModel}
                    setShowModel={setShowModel}
                  />
                )
              )
            : <>
                {/* {experienceState?.enableSnapView
                  ? <ExperienceCameraControls/>
                  : <ExperienceOrbitControls data={data}/>
                } */}
                {/* <ExperienceCameraControls data={data}/> */}
                <ExperienceOrbitControls data={data}/>
                {experienceState?.mode360 && <Experience360 data={data}/>}
                {experienceState?.modeModel && <ExperienceModel data={data}/>}
                {/* <CameraDebugWrapper /> */}
              </>
          }
        </Suspense>
      </Canvas>

      {/* AR DOM Overlay UI */}
      {experienceState?.modeAR && useNewARSystem && (
        <ARDOMOverlay
          placedModels={placedModels.length}
          reticleType={reticleType}
          currentHitPose={!!currentHitPose2}
          hitTestReady={hitTestReady}
          sessionActive={sessionActive}
          onCycleReticle={cycleReticleType}
          onClearModels={clearAllModels}
          onTestPlace={handleControllerSelect}
          onToggleARSystem={() => setUseNewARSystem(false)}
        />
      )}

      {/* Legacy AR UI */}
      {experienceState?.modeAR && !useNewARSystem && (
        <>
          <ExperienceARUI
            showError={showError}
            errorMessage={errorMessage}
            setShowError={setShowError}
            showModel={showModel}
            resetModelPlacement={resetModelPlacement}
          />
          <div className="absolute top-4 left-4 z-50">
            <button
              onClick={() => setUseNewARSystem(true)}
              className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm"
            >
              Use New AR System
            </button>
          </div>
        </>
      )}

      {/* <Loader/> */}
    </CameraControlsErrorBoundary>
  )
}