'use client'
import { useRef, useEffect, useState } from 'react'
import { useThree, useFrame } from '@react-three/fiber'
import { degToRad } from 'three/src/math/MathUtils'
import * as THREE from 'three'

export default function ARReticle({ 
  hitTestSource, 
  onHitPoseUpdate,
  visible = true,
  size = 0.15,
  color = "#00ff00",
  opacity = 0.8
}) {
  const { gl } = useThree()
  const reticleRef = useRef()
  const [isVisible, setIsVisible] = useState(false)
  const currentHitPose = useRef(null)

  // Hit test frame loop
  useFrame((state, delta, frame) => {
    if (!frame || !hitTestSource || !reticleRef.current || !visible) {
      if (isVisible) {
        setIsVisible(false)
        reticleRef.current.visible = false
      }
      return
    }

    try {
      // Get hit test results
      const hitTestResults = frame.getHitTestResults(hitTestSource)
      
      if (hitTestResults.length > 0) {
        const hit = hitTestResults[0]
        const referenceSpace = gl.xr.getReferenceSpace()

        if (referenceSpace) {
          const hitPose = hit.getPose(referenceSpace)

          if (hitPose) {
            // Check if surface is horizontal (ground/floor only)
            const matrix = new THREE.Matrix4().fromArray(hitPose.transform.matrix)
            const normal = new THREE.Vector3(0, 1, 0)
            normal.applyMatrix4(matrix)
            normal.normalize()

            // Calculate angle between surface normal and up vector
            const upVector = new THREE.Vector3(0, 1, 0)
            const angle = Math.acos(normal.dot(upVector))
            const angleInDegrees = (angle * 180) / Math.PI

            // Only show reticle on horizontal surfaces (within 30 degrees of horizontal)
            const isHorizontal = angleInDegrees < 30

            if (isHorizontal) {
              // Store current hit pose
              currentHitPose.current = hitPose

              // Update reticle position and rotation
              reticleRef.current.matrix.copy(matrix)
              reticleRef.current.matrixAutoUpdate = false

              // Make reticle visible
              if (!isVisible) {
                setIsVisible(true)
                reticleRef.current.visible = true
                console.log('🎯 Reticle visible - horizontal surface detected')
              }

              // Notify parent of hit pose update
              onHitPoseUpdate?.(hitPose, hit)
            } else {
              // Hide reticle on vertical surfaces
              if (isVisible) {
                setIsVisible(false)
                reticleRef.current.visible = false
                currentHitPose.current = null
                console.log('🎯 Reticle hidden - vertical surface ignored')
                onHitPoseUpdate?.(null, null)
              }
            }
          }
        }
      } else {
        // Hide reticle when no surface detected
        if (isVisible) {
          setIsVisible(false)
          reticleRef.current.visible = false
          currentHitPose.current = null
          console.log('🎯 Reticle hidden - no surface detected')
          onHitPoseUpdate?.(null, null)
        }
      }
    } catch (error) {
      console.error('❌ Hit test error:', error)
      if (isVisible) {
        setIsVisible(false)
        reticleRef.current.visible = false
      }
    }
  })

  // Get current hit pose (for external access)
  const getCurrentHitPose = () => currentHitPose.current

  // Expose methods to parent
  useEffect(() => {
    if (reticleRef.current) {
      reticleRef.current.getCurrentHitPose = getCurrentHitPose
    }
  }, [])

  return (
    <group
      ref={reticleRef}
      name="ARReticle"
      visible={false}
      matrixAutoUpdate={false}
    >
      {/* Main reticle ring */}
      <mesh rotation-x={degToRad(-90)}>
        <ringGeometry args={[size * 0.7, size, 32]} />
        <meshBasicMaterial
          color={color}
          transparent={true}
          opacity={opacity}
          side={THREE.DoubleSide}
          depthTest={false}
          depthWrite={false}
        />
      </mesh>
      
      {/* Inner dot */}
      <mesh rotation-x={degToRad(-90)} position={[0, 0.001, 0]}>
        <circleGeometry args={[size * 0.2, 16]} />
        <meshBasicMaterial
          color={color}
          transparent={true}
          opacity={opacity * 1.2}
          depthTest={false}
          depthWrite={false}
        />
      </mesh>
      
      {/* Animated pulse ring */}
      <PulseRing 
        size={size} 
        color={color} 
        opacity={opacity * 0.5}
      />
    </group>
  )
}

// Animated pulse ring component
function PulseRing({ size, color, opacity }) {
  const ringRef = useRef()
  const [scale, setScale] = useState(1)

  useFrame((state) => {
    if (ringRef.current) {
      // Animate pulse effect
      const pulseScale = 1 + Math.sin(state.clock.elapsedTime * 3) * 0.3
      ringRef.current.scale.setScalar(pulseScale)
      
      // Animate opacity
      const pulseOpacity = opacity * (0.5 + Math.sin(state.clock.elapsedTime * 3) * 0.3)
      ringRef.current.material.opacity = pulseOpacity
    }
  })

  return (
    <mesh 
      ref={ringRef}
      rotation-x={degToRad(-90)} 
      position={[0, 0.002, 0]}
    >
      <ringGeometry args={[size * 1.2, size * 1.4, 32]} />
      <meshBasicMaterial
        color={color}
        transparent={true}
        opacity={opacity}
        side={THREE.DoubleSide}
        depthTest={false}
        depthWrite={false}
      />
    </mesh>
  )
}

// Custom reticle variants
export function CrosshairReticle({ hitTestSource, onHitPoseUpdate, visible = true }) {
  return (
    <ARReticle
      hitTestSource={hitTestSource}
      onHitPoseUpdate={onHitPoseUpdate}
      visible={visible}
      size={0.1}
      color="#ff6600"
      opacity={0.9}
    />
  )
}

export function TargetReticle({ hitTestSource, onHitPoseUpdate, visible = true }) {
  const reticleRef = useRef()
  
  return (
    <group ref={reticleRef}>
      <ARReticle
        hitTestSource={hitTestSource}
        onHitPoseUpdate={onHitPoseUpdate}
        visible={visible}
        size={0.2}
        color="#0099ff"
        opacity={0.7}
      />
      
      {/* Additional crosshair lines */}
      <mesh rotation-x={degToRad(-90)} position={[0, 0.003, 0]}>
        <planeGeometry args={[0.02, 0.3]} />
        <meshBasicMaterial
          color="#0099ff"
          transparent={true}
          opacity={0.8}
          depthTest={false}
          depthWrite={false}
        />
      </mesh>
      
      <mesh rotation-x={degToRad(-90)} rotation-z={degToRad(90)} position={[0, 0.003, 0]}>
        <planeGeometry args={[0.02, 0.3]} />
        <meshBasicMaterial
          color="#0099ff"
          transparent={true}
          opacity={0.8}
          depthTest={false}
          depthWrite={false}
        />
      </mesh>
    </group>
  )
}
