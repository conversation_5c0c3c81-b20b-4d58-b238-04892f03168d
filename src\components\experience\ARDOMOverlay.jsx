'use client'
import { useEffect, useState } from 'react'
import { createPortal } from 'react-dom'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience'
import { TbAugmentedReality2, TbHexagon3D, TbView360 } from 'react-icons/tb'

export default function ARDOMOverlay({ 
  placedModels, 
  reticleType, 
  currentHitPose, 
  hitTestReady, 
  sessionActive,
  onCycleReticle,
  onClearModels,
  onTestPlace,
  onToggleARSystem
}) {
  const { experienceState, experienceDispatch } = useExperienceContext()
  const [overlayContainer, setOverlayContainer] = useState(null)

  useEffect(() => {
    // Find or create the DOM overlay container
    let container = document.getElementById('ar-overlay-container')
    if (!container) {
      container = document.createElement('div')
      container.id = 'ar-overlay-container'
      container.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1000;
      `
      document.body.appendChild(container)
    }
    setOverlayContainer(container)

    return () => {
      // Cleanup on unmount
      if (container && container.parentNode) {
        container.parentNode.removeChild(container)
      }
    }
  }, [])

  const modes = [
    { icon: <TbView360 className="text-2xl" />, name: '360' },
    { icon: <TbHexagon3D className="text-2xl" />, name: 'Model' },
    { icon: <TbAugmentedReality2 className="text-2xl" />, name: 'AR' },
  ]

  const handleModeSwitch = (index) => {
    if (index === 0) {
      experienceDispatch({ type: ACTIONS_EXPERIENCE.MODE_360 })
    } else if (index === 1) {
      experienceDispatch({ type: ACTIONS_EXPERIENCE.MODE_MODEL })
    } else if (index === 2) {
      experienceDispatch({ type: ACTIONS_EXPERIENCE.MODE_AR })
    }
  }

  if (!overlayContainer) {
    return null
  }

  return createPortal(
    <div className="ar-overlay-ui" style={{ pointerEvents: 'none', width: '100%', height: '100%' }}>
      {/* Mode Switcher - Top Center */}
      <div 
        className="absolute top-4 left-1/2 transform -translate-x-1/2 z-50"
        style={{ pointerEvents: 'auto' }}
      >
        <div className="flex gap-2 bg-black/70 text-white p-2 rounded-lg shadow-lg">
          {modes.map((mode, index) => (
            <button
              key={index}
              onClick={() => handleModeSwitch(index)}
              className={`p-3 rounded-lg transition-colors ${
                (index === 0 && experienceState?.mode360) ||
                (index === 1 && experienceState?.modeModel) ||
                (index === 2 && experienceState?.modeAR)
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-600 hover:bg-gray-500 text-gray-200'
              }`}
              title={mode.name}
            >
              {mode.icon}
            </button>
          ))}
        </div>
      </div>

      {/* Status Indicator - Top Right */}
      <div
        className="absolute top-4 right-4 z-50"
        style={{ pointerEvents: 'none' }}
      >
        <div className="bg-black/70 text-white p-3 rounded-lg shadow-lg">
          <div className="space-y-2 text-xs">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${sessionActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span>AR Session</span>
            </div>
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${currentHitPose ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
              <span>Surface Detection</span>
            </div>
            {placedModels > 0 && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                <span>{placedModels} Model{placedModels !== 1 ? 's' : ''} Placed</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Clear Button - Only show when models are placed */}
      {placedModels > 0 && (
        <div
          className="absolute top-4 right-4 mt-20 z-50"
          style={{ pointerEvents: 'auto' }}
        >
          <button
            onClick={onClearModels}
            className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg shadow-lg transition-colors text-sm"
          >
            Clear All Models
          </button>
        </div>
      )}

      {/* Instructions - Bottom Center */}
      <div
        className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-50"
        style={{ pointerEvents: 'none' }}
      >
        <div className="bg-black/70 text-white p-3 rounded-lg shadow-lg text-center max-w-sm">
          {!sessionActive ? (
            <p className="text-sm">Starting AR session...</p>
          ) : !currentHitPose ? (
            <p className="text-sm">Point your device at a flat surface</p>
          ) : (
            <p className="text-sm">Tap to place a building</p>
          )}
        </div>
      </div>
    </div>,
    overlayContainer
  )
}
