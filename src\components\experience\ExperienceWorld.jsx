'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense, useRef, useEffect, useState } from 'react'
import ExperienceAR, { ExperienceARUI } from './ExperienceAR'
import Experience360 from './Experience360'
import ExperienceModel from './ExperienceModel'
import ExperienceOrbitControls from './ExperienceOrbitControls'
import CameraControlsErrorBoundary from './CameraControlsErrorBoundary'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import ExperienceCameraControls from './ExperienceCameraControls'
import LoadingSpinner from '../LoadingSpinner'

// New AR System Components
import ARSessionManager from './ARSessionManager'
import ARReticle, { CrosshairReticle, TargetReticle } from './ARReticle'
import ARController, { ARControllerVisual, ARTouchHandler } from './ARController'
// import ARObjectManager, { useARObjectManager } from './ARObjectGenerator'
import ExperienceModelAR from './ExperienceModelAR'
import * as THREE from 'three'
export default function ExperienceWorld({data}) {
  const {experienceState}=useExperienceContext()
  const previousModeAR = useRef(experienceState?.modeAR)

  // Legacy AR state management (for old ExperienceAR component)
  const [errorMessage, setErrorMessage] = useState(null)
  const [showError, setShowError] = useState(false)
  const [showModel, setShowModel] = useState(false)

  // Legacy refs for AR functionality
  const refModelAR = useRef(null)
  const refSessionAR = useRef(null)
  const refController = useRef(null)
  const refReticle = useRef(null)
  const glSession = useRef(null)
  const hitTestSource = useRef(null)
  const currentHitPose = useRef(null)

  // New AR System State
  const [useNewARSystem, setUseNewARSystem] = useState(true)
  const [arSessionData, setArSessionData] = useState(null)
  const [currentHitPose2, setCurrentHitPose2] = useState(null)
  const [placedModels, setPlacedModels] = useState([]) // Array of placed ExperienceModelAR instances
  const [reticleType, setReticleType] = useState('default') // 'default', 'crosshair', 'target'

  const resetModelPlacement = () => {
    console.log('Resetting model placement')
    setShowModel(false)
    if (refModelAR.current) {
      refModelAR.current.visible = false
    }
    if (refReticle.current) {
      refReticle.current.visible = false
    }
  }

  // New AR System Handlers
  const handleARSessionStart = (session) => {
    console.log('🚀 New AR session started:', session)
  }

  const handleARSessionEnd = () => {
    console.log('🛑 New AR session ended')
    setCurrentHitPose2(null)
    setPlacedModels([])
  }

  const handleHitTestReady = (hitTestSource) => {
    console.log('🎯 Hit test source ready:', hitTestSource)
  }

  const handleHitPoseUpdate = (hitPose) => {
    setCurrentHitPose2(hitPose)
  }

  const handleControllerSelect = () => {
    console.log('🎮 Controller select triggered, currentHitPose2:', !!currentHitPose2)

    if (currentHitPose2) {
      // Extract position and rotation from hit pose
      const matrix = new THREE.Matrix4().fromArray(currentHitPose2.transform.matrix)
      const position = new THREE.Vector3()
      const rotation = new THREE.Euler()
      const scale = new THREE.Vector3()

      matrix.decompose(position, new THREE.Quaternion(), scale)
      rotation.setFromRotationMatrix(matrix)

      // Create new model placement
      const newModel = {
        id: Math.random().toString(36).substring(2, 11),
        position: [position.x, position.y, position.z],
        rotation: [rotation.x, rotation.y, rotation.z],
        scale: [0.5, 0.5, 0.5], // Appropriate scale for AR viewing
        hitPose: currentHitPose2,
        createdAt: Date.now()
      }

      // Add to placed models (limit to 5 models max)
      setPlacedModels(prev => {
        const updated = [...prev, newModel]
        if (updated.length > 5) {
          updated.shift() // Remove oldest model
        }
        return updated
      })

      console.log('� Placed ExperienceModelAR at:', position)
      console.log('🏠 Total placed models:', placedModels.length + 1)
    } else {
      console.warn('⚠️ No hit pose available for model placement')
    }
  }

  const handleModelRemoved = (modelId) => {
    setPlacedModels(prev => prev.filter(model => model.id !== modelId))
    console.log('➖ Model removed:', modelId)
  }

  const clearAllModels = () => {
    setPlacedModels([])
    console.log('🧹 All models cleared')
  }

  const cycleReticleType = () => {
    const types = ['default', 'crosshair', 'target']
    const currentIndex = types.indexOf(reticleType)
    const nextIndex = (currentIndex + 1) % types.length
    setReticleType(types[nextIndex])
    console.log('🎯 Reticle type changed to:', types[nextIndex])
  }

  // Handle XR session termination when switching out of AR mode
  useEffect(() => {
    const currentModeAR = experienceState?.modeAR

    // If we were in AR mode and now we're not, terminate the XR session
    if (previousModeAR.current && !currentModeAR) {
      console.log('Switching out of AR mode, terminating XR session...')

      // Simple fallback cleanup
      const terminateActiveSession = async () => {
        try {
          const canvas = document.querySelector('canvas')
          if (canvas) {
            const gl = canvas.getContext('webgl2') || canvas.getContext('webgl')
            if (gl && gl.xr && gl.xr.getSession()) {
              console.log('Found active XR session, ending it...')
              await gl.xr.getSession().end()
              gl.xr.enabled = false
              gl.xr.setAnimationLoop(null)
              console.log('XR session terminated successfully')
            }
          }
        } catch (error) {
          console.error('Error terminating XR session:', error)
        }
      }

      terminateActiveSession()
    }

    // Update the previous mode reference
    previousModeAR.current = currentModeAR
  }, [experienceState?.modeAR])

  // console.log('ExperienceWorld:',experienceState?.enableSnapView)
  return (
    <CameraControlsErrorBoundary>
      <Canvas>
        <Suspense fallback={<LoadingSpinner/>}>
          {experienceState?.modeAR
            ? (
                useNewARSystem ? (
                  // New AR System
                  <ARSessionManager
                    onSessionStart={handleARSessionStart}
                    onSessionEnd={handleARSessionEnd}
                    onHitTestReady={handleHitTestReady}
                  >
                    {(sessionData) => (
                      <>
                        {/* Lighting for AR objects */}
                        <ambientLight intensity={0.6} />
                        <directionalLight position={[10, 10, 5]} intensity={0.8} />

                        {/* AR Reticle - Always show a basic reticle */}
                        <group>
                          {sessionData.hitTestReady ? (
                            <>
                              {reticleType === 'default' && (
                                <ARReticle
                                  hitTestSource={sessionData.hitTestSource}
                                  onHitPoseUpdate={handleHitPoseUpdate}
                                  visible={true}
                                />
                              )}
                              {reticleType === 'crosshair' && (
                                <CrosshairReticle
                                  hitTestSource={sessionData.hitTestSource}
                                  onHitPoseUpdate={handleHitPoseUpdate}
                                  visible={true}
                                />
                              )}
                              {reticleType === 'target' && (
                                <TargetReticle
                                  hitTestSource={sessionData.hitTestSource}
                                  onHitPoseUpdate={handleHitPoseUpdate}
                                  visible={true}
                                />
                              )}
                            </>
                          ) : (
                            // Fallback reticle when hit test is not ready
                            <mesh position={[0, 0, -1]}>
                              <ringGeometry args={[0.1, 0.15, 32]} />
                              <meshBasicMaterial
                                color="#ff0000"
                                transparent={true}
                                opacity={0.8}
                                side={THREE.DoubleSide}
                              />
                            </mesh>
                          )}

                          {/* Simple always-visible reticle for debugging */}
                          <mesh position={[0, 0, -0.5]} rotation-x={-Math.PI / 2}>
                            <ringGeometry args={[0.08, 0.12, 16]} />
                            <meshBasicMaterial
                              color="#ffff00"
                              transparent={true}
                              opacity={0.6}
                              side={THREE.DoubleSide}
                              depthTest={false}
                              depthWrite={false}
                            />
                          </mesh>
                        </group>

                        {/* Debug reticle - always visible */}
                        <mesh position={[0, -0.5, -2]}>
                          <ringGeometry args={[0.05, 0.1, 16]} />
                          <meshBasicMaterial
                            color="#00ff00"
                            transparent={true}
                            opacity={1.0}
                            side={THREE.DoubleSide}
                          />
                        </mesh>

                        {/* AR Controller */}
                        <ARController
                          onSelect={handleControllerSelect}
                          enabled={sessionData.isSessionActive}
                        />

                        {/* AR Controller Visual */}
                        <ARControllerVisual
                          controllerIndex={0}
                          showRay={true}
                          rayColor="#ffffff"
                          rayLength={5}
                        />

                        {/* Placed ExperienceModelAR instances */}
                        {placedModels.map((model) => (
                          <group
                            key={model.id}
                            position={model.position}
                            rotation={model.rotation}
                            scale={model.scale}
                          >
                            <ExperienceModelAR
                              data={data}
                              refModelAR={null}
                            />
                          </group>
                        ))}

                        {/* Touch Handler for mobile */}
                        <ARTouchHandler
                          onTap={handleControllerSelect}
                          enabled={sessionData.isSessionActive}
                        />
                      </>
                    )}
                  </ARSessionManager>
                ) : (
                  // Legacy AR System
                  <ExperienceAR
                    data={data}
                    refModelAR={refModelAR}
                    refSessionAR={refSessionAR}
                    refController={refController}
                    refReticle={refReticle}
                    glSession={glSession}
                    hitTestSource={hitTestSource}
                    currentHitPose={currentHitPose}
                    setErrorMessage={setErrorMessage}
                    setShowError={setShowError}
                    showModel={showModel}
                    setShowModel={setShowModel}
                  />
                )
              )
            : <>
                {/* {experienceState?.enableSnapView
                  ? <ExperienceCameraControls/>
                  : <ExperienceOrbitControls data={data}/>
                } */}
                {/* <ExperienceCameraControls data={data}/> */}
                <ExperienceOrbitControls data={data}/>
                {experienceState?.mode360 && <Experience360 data={data}/>}
                {experienceState?.modeModel && <ExperienceModel data={data}/>}
                {/* <CameraDebugWrapper /> */}
              </>
          }
        </Suspense>
      </Canvas>

      {/* AR UI Components outside Canvas */}
      {experienceState?.modeAR && (
        <>
          {useNewARSystem ? (
            // New AR System UI
            <div className="absolute top-4 right-4 z-50 space-y-2">
              <div className="bg-black/70 text-white p-3 rounded-lg">
                <h3 className="font-bold text-sm mb-2">AR Controls</h3>
                <div className="space-y-2 text-xs">
                  <div>Models: {placedModels.length}</div>
                  <div>Reticle: {reticleType}</div>
                  <div>Hit Pose: {currentHitPose2 ? '✅' : '❌'}</div>
                  <div>AR Mode: {experienceState?.modeAR ? '✅' : '❌'}</div>
                </div>
                <div className="flex gap-2 mt-3">
                  <button
                    onClick={cycleReticleType}
                    className="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs"
                  >
                    Cycle Reticle
                  </button>
                  <button
                    onClick={clearAllModels}
                    className="bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs"
                  >
                    Clear All
                  </button>
                </div>
                <div className="mt-2">
                  <button
                    onClick={() => setUseNewARSystem(false)}
                    className="bg-gray-600 hover:bg-gray-700 px-2 py-1 rounded text-xs"
                  >
                    Use Legacy AR
                  </button>
                </div>
              </div>
            </div>
          ) : (
            // Legacy AR System UI
            <>
              <ExperienceARUI
                showError={showError}
                errorMessage={errorMessage}
                setShowError={setShowError}
                showModel={showModel}
                resetModelPlacement={resetModelPlacement}
              />
              <div className="absolute top-4 left-4 z-50">
                <button
                  onClick={() => setUseNewARSystem(true)}
                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm"
                >
                  Use New AR System
                </button>
              </div>
            </>
          )}
        </>
      )}

      {/* <Loader/> */}
    </CameraControlsErrorBoundary>
  )
}