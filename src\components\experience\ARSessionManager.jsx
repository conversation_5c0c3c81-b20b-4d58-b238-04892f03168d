'use client'
import { useRef, useEffect, useState } from 'react'
import { useThree } from '@react-three/fiber'

export default function ARSessionManager({ 
  onSessionStart, 
  onSessionEnd, 
  onHitTestReady,
  children 
}) {
  const { gl } = useThree()
  const sessionRef = useRef(null)
  const hitTestSourceRef = useRef(null)
  const [isSessionActive, setIsSessionActive] = useState(false)
  const [hitTestReady, setHitTestReady] = useState(false)
  const [error, setError] = useState(null)

  // Initialize AR session
  const startARSession = async () => {
    try {
      console.log('🚀 Starting AR session...')

      // Check AR support
      if (!navigator.xr) {
        throw new Error('WebXR not supported')
      }

      const supported = await navigator.xr.isSessionSupported('immersive-ar')
      if (!supported) {
        throw new Error('AR not supported on this device')
      }

      // Create or get DOM overlay container
      let overlayContainer = document.getElementById('ar-overlay-container')
      if (!overlayContainer) {
        overlayContainer = document.createElement('div')
        overlayContainer.id = 'ar-overlay-container'
        overlayContainer.style.cssText = `
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
          z-index: 1000;
        `
        document.body.appendChild(overlayContainer)
      }

      // Request AR session with DOM overlay
      const session = await navigator.xr.requestSession('immersive-ar', {
        requiredFeatures: ['hit-test'],
        optionalFeatures: ['dom-overlay'],
        domOverlay: { root: overlayContainer }
      })

      sessionRef.current = session
      
      // Setup Three.js XR
      gl.xr.enabled = true
      gl.xr.setReferenceSpaceType('local')
      await gl.xr.setSession(session)
      
      setIsSessionActive(true)
      console.log('✅ AR session started successfully')
      
      // Setup hit test source
      await setupHitTestSource(session)
      
      // Notify parent component
      onSessionStart?.(session)
      
    } catch (error) {
      console.error('❌ Failed to start AR session:', error)
      setError(error.message)
    }
  }

  // Setup hit test source
  const setupHitTestSource = async (session) => {
    try {
      console.log('🎯 Setting up hit test source...')
      
      const referenceSpace = await session.requestReferenceSpace('viewer')
      const hitTestSource = await session.requestHitTestSource({ 
        space: referenceSpace 
      })
      
      hitTestSourceRef.current = hitTestSource
      setHitTestReady(true)
      
      console.log('✅ Hit test source ready')
      onHitTestReady?.(hitTestSource)
      
    } catch (error) {
      console.error('❌ Failed to setup hit test source:', error)
      setError('Hit test not available')
    }
  }

  // End AR session
  const endARSession = async () => {
    try {
      console.log('🛑 Ending AR session...')

      if (sessionRef.current) {
        await sessionRef.current.end()
      }

      // Cleanup DOM overlay container
      const overlayContainer = document.getElementById('ar-overlay-container')
      if (overlayContainer) {
        overlayContainer.remove()
      }

      // Cleanup
      sessionRef.current = null
      hitTestSourceRef.current = null
      setIsSessionActive(false)
      setHitTestReady(false)
      setError(null)

      // Reset Three.js XR
      gl.xr.enabled = false
      gl.xr.setAnimationLoop(null)

      console.log('✅ AR session ended')
      onSessionEnd?.()

    } catch (error) {
      console.error('❌ Error ending AR session:', error)
    }
  }

  // Session event handlers
  useEffect(() => {
    if (!sessionRef.current) return

    const handleSessionEnd = () => {
      console.log('📱 AR session ended by system')
      setIsSessionActive(false)
      setHitTestReady(false)
      sessionRef.current = null
      hitTestSourceRef.current = null
      onSessionEnd?.()
    }

    sessionRef.current.addEventListener('end', handleSessionEnd)
    
    return () => {
      sessionRef.current?.removeEventListener('end', handleSessionEnd)
    }
  }, [isSessionActive, onSessionEnd])

  // Auto-start session when component mounts
  useEffect(() => {
    startARSession()
    
    return () => {
      endARSession()
    }
  }, [])

  // Provide session data to children
  const sessionData = {
    session: sessionRef.current,
    hitTestSource: hitTestSourceRef.current,
    isSessionActive,
    hitTestReady,
    error,
    startSession: startARSession,
    endSession: endARSession
  }

  return (
    <>
      {children(sessionData)}
      
      {/* Error display */}
      {error && (
        <div className="absolute top-4 left-4 bg-red-600 text-white p-3 rounded-lg z-50 max-w-sm">
          <h3 className="font-bold text-sm mb-1">AR Error</h3>
          <p className="text-xs mb-2">{error}</p>
          <button
            onClick={() => {
              setError(null)
              startARSession()
            }}
            className="bg-red-800 hover:bg-red-900 px-2 py-1 rounded text-xs"
          >
            Retry
          </button>
        </div>
      )}
    </>
  )
}

// Export hook for accessing session data
export function useARSession() {
  const sessionContext = useRef(null)
  
  const setSessionContext = (data) => {
    sessionContext.current = data
  }
  
  return {
    sessionData: sessionContext.current,
    setSessionContext
  }
}
