import { buildings } from '@/libs/blg'
import React, { Suspense } from 'react'
import BuildPageComponent from '@/components/BuildPageComponent'
import Scroller<PERSON>rapper from '@/components/ScrollerWrapper'
import Image from 'next/image'
import LoadingComponent from '@/components/LoadingComponent'
import ExperienceWrapper from '@/components/experience/ExperienceWrapper'
import { settings } from '@/libs/siteSettings'

export default async function page({params}) {
  const cssSection='sectionWrapper flex w-full h-full flex-none relative overflow-hidden'
  const {id}=await params
  let data = null
  try {
    const response = await fetch(`${settings.url}/api/buildings/${id}`)

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return <div className='flex items-center justify-center w-full h-full'>Error loading building: {response.statusText}</div>;
    }

    const result = await response.json();
    data = result.building || result;

  } catch (error) {
    console.error('Failed to fetch building data:', error);
    return <div className='flex items-center justify-center w-full h-full'>Failed to load building data due to a network error.</div>;
  }

  if (!data) {
    return <div className='flex items-center justify-center w-full h-full'>Building not found or no data available.</div>;
  }

  // console.log('ProductPage:',data)
  return (
    <div className='flex w-full h-svh'>
      <BuildPageComponent data={data}>
        {/* renders wrapper */}
        <section className={cssSection}>
          <ScrollerWrapper>
            {data?.renders?.length > 0 ? data.renders.map((i,index)=>
              <div key={index} className='flex brightness-90 relative w-full h-full flex-none'>
                <Image priority src={i?.url} className='object-cover flex-none' alt="view" fill/>
              </div>
            ) : (
              <div className='flex items-center justify-center w-full h-full text-gray-500'>
                No renders available
              </div>
            )}
          </ScrollerWrapper>
        </section>

        {/* drawings wrapper */}
        <section className={cssSection}>
          <ScrollerWrapper>
            {data?.drawings?.length > 0 ? data.drawings.map((i,index)=>
              <div key={index} className='flex brightness-90 relative w-full h-full flex-none'>
                <Image priority src={i?.url} className='object-cover flex-none' alt="view" fill/>
              </div>
            ) : (
              <div className='flex items-center justify-center w-full h-full text-gray-500'>
                No drawings available
              </div>
            )}
          </ScrollerWrapper>
        </section>

        {/* experience wrapper */}
        <section className={cssSection}>
          <Suspense fallback={<LoadingComponent/>}>
            <ExperienceWrapper data={data}/>
          </Suspense>
        </section>
      </BuildPageComponent>
    </div>
  )
}
